// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
// import { ChevronLeft, Eye, FileText, Plus } from "lucide-react";
// import React, { useEffect, useRef, useState } from "react";
// import { useDragLayer, useDrop } from "react-dnd";
// import SinglePagePreview from "./SinglePagePreview";
// import DeviceButtons from "../../common/DeviceButtons";

// // Device sizes for responsive preview (similar to demo)
// const DEVICE_SIZES = {
//   mobile: { width: 375, height: 667 },
//   tablet: { width: 768, height: 1024 },
//   laptop: { width: 1200, height: 800 },
// };

// const TemplatePreview = ({
//   isPageLibraryOpen,
//   setIsPageLibraryOpen,
//   isTemplateStructureOpen,
//   setIsTemplateStructureOpen,
//   formData,
//   setFormData,
//   pages,
//   handleSave,
//   saving,
//   onCancel,
//   isDrop = true,
// }) => {
//   const [previewMode, setPreviewMode] = useState("laptop");
//   const [isDragging, setIsDragging] = useState(false);

//   // Global drag detection
//   const { isDraggingGlobal } = useDragLayer((monitor) => ({
//     isDraggingGlobal: monitor?.isDragging(),
//   }));

//   // Update local dragging state based on global drag state
//   useEffect(() => {
//     if (isDraggingGlobal) {
//       setIsDragging(true);
//     } else {
//       // Small delay to allow drop to complete
//       const timer = setTimeout(() => setIsDragging(false), 100);
//       return () => clearTimeout(timer);
//     }
//   }, [isDraggingGlobal]);
//   // Add page to template
//   const addPageToTemplate = (page) => {
//     console.log(page, "kjfgbjfd");
//     const templatePage = {
//       ...page,
//       id: page.id,
//       name: page.name,
//       slug: page.slug,
//       version: "v1",
//       url: `/${page.slug}`,
//       type: "static",
//       showNavbar: true,
//       navPosition: formData.pages?.length || 0,
//       order: formData.pages?.length || 0,
//     };

//     const updatedPages = [...(formData.pages || []), templatePage];
//     setFormData({ ...formData, pages: updatedPages });
//   };

//   // Drop zone for template preview area
//   const TemplateDropZone = ({ isDragging, setIsDragging }) => {
//     const [{ isOver, canDrop }, drop] = useDrop(
//       () => ({
//         accept: "PAGE_ITEM",
//         drop: (item, monitor) => {
//           if (monitor.didDrop()) return;
//           console.log("Dropping page:", item.page);
//           addPageToTemplate(item.page);
//           setIsDragging(false);
//         },
//         collect: (monitor) => ({
//           isOver: monitor.isOver({ shallow: true }),
//           canDrop: monitor.canDrop(),
//         }),
//         hover: (_, monitor) => {
//           // Set dragging state when hovering
//           if (monitor.canDrop()) {
//             setIsDragging(true);
//           }
//         },
//       }),
//       [formData.pages, setIsDragging]
//     );

//     const showDropIndicator = isOver && canDrop;

//     return (
//       <div
//         ref={drop}
//         className={`tw-absolute tw-inset-0 tw-transition-all tw-duration-150 ${
//           isDragging || showDropIndicator ? "tw-z-30" : "tw-z-10"
//         } ${
//           showDropIndicator
//             ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
//             : ""
//         }`}
//         style={{
//           minHeight: "400px",
//           pointerEvents: "auto", // Always allow pointer events for drop zone
//         }}
//         onMouseLeave={() => {
//           // Reset dragging state when mouse leaves the drop zone
//           if (!isOver) {
//             setIsDragging(false);
//           }
//         }}
//       >
//         {showDropIndicator && (
//           <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
//             <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
//               <div className="tw-flex tw-items-center tw-space-x-3">
//                 <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
//                 <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
//                   Drop to add page to template
//                 </p>
//                 <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     );
//   };

//   return (
//     <>
//       <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col">
//         {/* Top Toolbar */}
//         <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
//           {!isPageLibraryOpen && (
//             <div className="tw-flex tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
//               <Tooltip title="Show Page Library">
//                 <button
//                   onClick={() => setIsPageLibraryOpen(true)}
//                   className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
//                 >
//                   <ChevronLeft size={30} className="tw-rotate-180" />
//                 </button>
//               </Tooltip>
//             </div>
//           )}
//           <div className="tw-p-3 md:tw-p-[21px] tw-flex tw-w-full tw-items-center tw-justify-between">
//             <DeviceButtons
//               previewMode={previewMode}
//               setPreviewMode={setPreviewMode}
//             />

//             <div className="tw-flex tw-items-center tw-space-x-2">
//               <button
//                 onClick={onCancel}
//                 className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
//               >
//                 Cancel
//               </button>

//               <Button
//                 type="primary"
//                 size="large"
//                 onClick={handleSave}
//                 title=""
//                 disabled={saving}
//                 className="tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
//               >
//                 {saving ? (
//                   <span className="tw-flex tw-gap-1 tw-items-center">
//                     <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
//                     Saving...
//                   </span>
//                 ) : (
//                   <>Save</>
//                 )}
//               </Button>
//             </div>
//           </div>
//           {!isTemplateStructureOpen && (
//             <div className="tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
//               <Tooltip title="Show Template Structure">
//                 <button
//                   onClick={() => setIsTemplateStructureOpen(true)}
//                   className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
//                 >
//                   <ChevronLeft size={30} className="tw-rotate-180" />
//                 </button>
//               </Tooltip>
//             </div>
//           )}
//         </div>

//         {/* Preview Content */}
//         <div className="tw-flex-1 tw-overflow-auto tw-p-4 tw-relative tw-bg-gray-50">
//           {formData?.pages?.length > 0 ? (
//             <div className="tw-space-y-4">
//               {formData.pages.map((templatePage) => {
//                 // const originalPage = pages.find(
//                 //   (p) => p.id === templatePage.id
//                 // );
//                 return (
//                   <div key={templatePage.id}>
//                     <SinglePagePreview
//                       templatePage={templatePage}
//                       // originalPage={originalPage}
//                       previewMode={previewMode}
//                       isDragging={isDragging}
//                     />
//                   </div>
//                 );
//               })}
//             </div>
//           ) : (
//             <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
//               <div className="tw-text-center">
//                 <Plus className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
//                 <p className="tw-text-gray-500 tw-mb-2">
//                   No pages in template yet
//                 </p>
//                 <p className="tw-text-sm tw-text-gray-400">
//                   Drag pages from the left panel to build your template
//                 </p>
//               </div>
//             </div>
//           )}
//           {isDrop && (
//             <TemplateDropZone
//               isDragging={isDragging}
//               setIsDragging={setIsDragging}
//             />
//           )}
//         </div>
//       </div>
//     </>
//   );
// };

// export default React.memo(TemplatePreview);

// TemplatePreview.jsx
import { Button, Tooltip } from "antd";
import { ChevronLeft, Plus } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useDragLayer, useDrop } from "react-dnd";
import SinglePagePreview from "./SinglePagePreview";
import DeviceButtons from "../../common/DeviceButtons";

const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const TemplatePreview = ({
  isPageLibraryOpen,
  setIsPageLibraryOpen,
  isTemplateStructureOpen,
  setIsTemplateStructureOpen,
  formData,
  setFormData,
  pages,
  handleSave,
  saving,
  onCancel,
  isDrop = true,
  // NEW:
  contentJSON = {}, // <—— add
  isContentAdding = false, // <—— add
}) => {
  const [previewMode, setPreviewMode] = useState("laptop");
  const [isDragging, setIsDragging] = useState(false);
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor?.isDragging(),
  }));

  useEffect(() => {
    if (isDraggingGlobal) setIsDragging(true);
    else {
      const t = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(t);
    }
  }, [isDraggingGlobal]);

  const addPageToTemplate = (page) => {
    const templatePage = {
      ...page,
      id: page.id,
      name: page.name,
      slug: page.slug,
      version: "v1",
      url: `/${page.slug}`,
      type: "static",
      showNavbar: true,
      navPosition: formData.pages?.length || 0,
      order: formData.pages?.length || 0,
    };
    const updatedPages = [...(formData.pages || []), templatePage];
    setFormData({ ...formData, pages: updatedPages });
  };

  const TemplateDropZone = ({ isDragging, setIsDragging }) => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: "PAGE_ITEM",
        drop: (item, monitor) => {
          if (monitor.didDrop()) return;
          addPageToTemplate(item.page);
          setIsDragging(false);
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
        hover: (_, monitor) => {
          if (monitor.canDrop()) setIsDragging(true);
        },
      }),
      [formData.pages, setIsDragging]
    );
    const showDropIndicator = isOver && canDrop;

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-transition-all tw-duration-150 ${
          isDragging || showDropIndicator ? "tw-z-30" : "tw-z-10"
        } ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{ minHeight: "400px", pointerEvents: "auto" }}
        onMouseLeave={() => {
          if (!isOver) setIsDragging(false);
        }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-rounded-full tw-animate-bounce tw-bg-blue-500"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add page to template
                </p>
                <div className="tw-w-3 tw-h-3 tw-rounded-full tw-animate-bounce tw-bg-blue-500"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col">
      {/* toolbar …(unchanged)… */}

      {/* Preview Content */}
      <div className="tw-flex-1 tw-overflow-auto tw-p-4 tw-relative tw-bg-gray-50">
        {formData?.pages?.length > 0 ? (
          <div className="tw-space-y-4">
            {formData.pages.map((templatePage) => (
              <div key={templatePage.id}>
                <SinglePagePreview
                  templatePage={templatePage}
                  previewMode={previewMode}
                  isDragging={isDragging}
                  // NEW:
                  contentJSON={contentJSON}
                  isContentAdding={isContentAdding}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
            <div className="tw-text-center">
              <Plus className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
              <p className="tw-text-gray-500 tw-mb-2">
                No pages in template yet
              </p>
              <p className="tw-text-sm tw-text-gray-400">
                Drag pages from the left panel to build your template
              </p>
            </div>
          </div>
        )}
        {isDrop && (
          <TemplateDropZone
            isDragging={isDragging}
            setIsDragging={setIsDragging}
          />
        )}
      </div>
    </div>
  );
};

export default React.memo(TemplatePreview);
