import React from 'react';

// Test component to verify content injection functionality
const ContentInjectionTest = () => {
  // Test HTML with data-component attributes
  const testHTML = `
    <div data-component="Hero Section">
      <h1>\${hero_title}</h1>
      <p>\${hero_subtitle}</p>
      <button>\${cta_text}</button>
    </div>
    <div data-component="Contact Form">
      <h2>\${form_title}</h2>
      <input placeholder="\${name_placeholder}" />
      <input placeholder="\${email_placeholder}" />
      <textarea placeholder="\${message_placeholder}"></textarea>
      <button>\${submit_text}</button>
    </div>
  `;

  // Test content JSON
  const testContentJSON = {
    "Home Page": {
      "Hero Section": {
        hero_title: "Welcome to Our Amazing Website",
        hero_subtitle: "We provide the best solutions for your business needs",
        cta_text: "Get Started Now"
      },
      "Contact Form": {
        form_title: "Contact Us Today",
        name_placeholder: "Your Full Name",
        email_placeholder: "<EMAIL>",
        message_placeholder: "Tell us about your project...",
        submit_text: "Send Message"
      }
    }
  };

  // Import the injection function
  const { injectContentIntoHTML } = require('./SinglePagePreview');

  const handleTest = () => {
    console.log("🧪 Testing Content Injection");
    console.log("Original HTML:", testHTML);
    console.log("Content JSON:", testContentJSON);
    
    try {
      const result = injectContentIntoHTML({
        html: testHTML,
        contentJSON: testContentJSON,
        pageName: "Home Page"
      });
      console.log("✅ Injection Result:", result);
    } catch (error) {
      console.error("❌ Injection Error:", error);
    }
  };

  return (
    <div className="tw-p-4 tw-bg-gray-100 tw-rounded-lg tw-m-4">
      <h3 className="tw-text-lg tw-font-bold tw-mb-4">Content Injection Test</h3>
      <button 
        onClick={handleTest}
        className="tw-bg-blue-500 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-blue-600"
      >
        Run Test
      </button>
      <div className="tw-mt-4 tw-text-sm">
        <p>Check the browser console for test results</p>
      </div>
    </div>
  );
};

export default ContentInjectionTest;
