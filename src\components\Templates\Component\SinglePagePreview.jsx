// import React, { useEffect, useRef, useState } from "react";
// import { FileText } from "lucide-react";
// import { generateGlobalPreviewHTML } from "../../Components/content";
// import { useDragLayer } from "react-dnd";

// // Device sizes for responsive preview
// const DEVICE_SIZES = {
//   mobile: { width: 375, height: 667 },
//   tablet: { width: 768, height: 1024 },
//   laptop: { width: 1200, height: 800 },
// };

// const SinglePagePreview = ({ templatePage, previewMode, isDragging }) => {
//   const containerRef = useRef(null);
//   const [scale, setScale] = useState(1);
//   // Get current device dimensions
//   const { width: deviceWidth, height: deviceHeight } =
//     DEVICE_SIZES[previewMode];

//   // Scale calculation function (similar to PagePreview)
//   const recalcScale = () => {
//     if (!containerRef.current) return;
//     const bounds = containerRef.current.getBoundingClientRect();
//     // Add padding to ensure device doesn't fill entire container
//     const availableWidth = bounds.width - (previewMode == "laptop" ? 15 : 15); // 150px padding on each side
//     const availableHeight = bounds.height - (previewMode == "laptop" ? 15 : 15); // 150px padding on top/bottom
//     const widthScale = availableWidth / deviceWidth;
//     const heightScale = availableHeight / deviceHeight;
//     requestAnimationFrame(() => {
//       setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
//     });
//   };

//   // Update scale on mount & when device changes
//   useEffect(() => {
//     recalcScale();
//     const resizeObserver = new ResizeObserver(recalcScale);
//     if (containerRef.current) {
//       resizeObserver.observe(containerRef.current);
//     }
//     return () => resizeObserver.disconnect();
//   }, [previewMode, deviceWidth, deviceHeight]);

//   // Template Page Preview Component - Single device frame like PagePreview
//   const TemplatePagePreview = ({ templatePage }) => {
//     const generatePagePreviewHTML = () => {
//       if (!templatePage) return "";
//       return generateGlobalPreviewHTML({
//         type: "page",
//         data: [],
//         // pageData: templatePage,
//         customCSS: templatePage.custom_css,
//         customJS: templatePage.custom_js,
//         title: templatePage.name || templatePage.name,
//         fullData: templatePage?.full_page_content || "",
//       });
//     };

//     return (
//       <div
//         className={`tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative `}
//         style={{
//           height: "auto",
//           minHeight:
//             previewMode == "laptop"
//               ? "320px"
//               : previewMode == "tablet"
//               ? "490px"
//               : "490px",
//         }}
//       >
//         {/* Virtual device frame - exactly like PagePreview */}
//         <div
//           className="device-frame tw-bg-white tw-rounded-xl  tw-border tw-border-gray-200 tw-absolute"
//           style={{
//             width: `${deviceWidth}px`,
//             height: `${deviceHeight}px`,
//             transform: `scale(${scale})`,
//             left: "50%",
//             top: "50%",
//             marginLeft: `-${deviceWidth / 2}px`,
//             marginTop: `-${
//               deviceHeight /
//               (previewMode == "laptop"
//                 ? 2.4
//                 : previewMode == "tablet"
//                 ? 2.4
//                 : 2.9)
//             }px`,
//             transition: "all 0.3s ease",
//             zIndex: isDragging ? 0 : 25,
//           }}
//         >
//           <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-rounded-xl">
//             {templatePage ? (
//               <iframe
//                 // srcDoc={templatePage?.full_page_content}
//                 srcDoc={generatePagePreviewHTML()}
//                 className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl tw-relative"
//                 title={`${templatePage.name} Preview`}
//                 style={{
//                   pointerEvents: isDragging ? "none" : "auto",
//                   background: "#fff",
//                 }}
//               />
//             ) : (
//               <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
//                 <div className="tw-text-center">
//                   <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
//                   <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
//                   <p className="tw-text-sm tw-text-gray-400">
//                     This page may have been deleted
//                   </p>
//                 </div>
//               </div>
//             )}
//           </div>
//         </div>
//       </div>
//     );
//   };

//   return (
//     <div
//       ref={containerRef}
//       style={{
//         height: "auto",
//         minHeight:
//           previewMode == "laptop"
//             ? `${29}rem`
//             : previewMode == "tablet"
//             ? `${42}rem`
//             : `${43}rem`,
//       }}
//       className={`tw-w-full  tw-overflow-auto tw-relative tw-rounded-lg `}
//     >
//       <TemplatePagePreview
//         key={templatePage.id}
//         templatePage={templatePage}
//         // originalPage={originalPage}
//       />
//     </div>
//   );
// };

// export default SinglePagePreview;

// SinglePagePreview.jsx
import React, { useEffect, useRef, useState } from "react";
import { FileText } from "lucide-react";
import { generateGlobalPreviewHTML } from "../../Components/content";

const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

/* ---------------------------
   Content Injection Utilities
----------------------------*/

// normalize (trim) for keys that may have trailing spaces
const norm = (s) => (typeof s === "string" ? s.trim() : s);

// get page → component → scope
const getComponentScope = (contentJSON, pageName, componentName) => {
  const page = contentJSON?.[norm(pageName)];
  if (!page) return null;
  return page[norm(componentName)] || null;
};

// resolve path like ["Final Testing Purpose","section Template ","ITEMS"] or ["section Template ","Title"]
const deepGet = (obj, pathArr) => {
  try {
    return pathArr.reduce((acc, k) => (acc == null ? acc : acc[norm(k)]), obj);
  } catch {
    return undefined;
  }
};

// replace ${key} using scope (object with primitive values)
const replaceScalars = (str, scope) => {
  if (!str || !scope || typeof scope !== "object") return str;
  return str.replace(/\$\{([^}]+)\}/g, (_, raw) => {
    const key = norm(raw);
    const val = scope[key];
    return val === undefined || val === null ? "" : String(val);
  });
};

// repeat handler: <div data-repeat="ITEMS"> ... ${name} ... </div>
const renderRepeats = (html, scope) => {
  if (!scope) return html;
  const re =
    /<([a-zA-Z][\w-]*)\b([^>]*\bdata-repeat\s*=\s*"([^"]+)"[^>]*)>([\s\S]*?)<\/\1>/g;

  return html.replace(re, (full, tag, attrs, repeatKeyRaw, inner) => {
    const repeatKey = norm(repeatKeyRaw);
    const arr = scope?.[repeatKey];

    if (!Array.isArray(arr) || arr.length === 0) {
      // If no items, remove the whole repeated element
      return "";
    }

    // Build each item block
    const blocks = arr.map((item) => {
      // support nested repeats inside inner via recursion
      let rendered = renderRepeats(inner, item);
      rendered = replaceScalars(rendered, item); // ${name}, ${state}, etc.
      return rendered;
    });

    return `<${tag}${attrs.replace(/\sdata-repeat="[^"]*"/, "")}>${blocks.join(
      ""
    )}</${tag}>`;
  });
};

// Final processor for a single component chunk
const processComponentHTML = (innerHTML, scope) => {
  // 1) render repeats first (they contain own ${subKey})
  let out = renderRepeats(innerHTML, scope);

  // 2) then replace scalars in this component scope
  out = replaceScalars(out, scope);

  return out;
};

// Process all components blocks with data-component="Component Name"
const injectByComponentBlocks = (html, pageName, contentJSON) => {
  const compRe =
    /<([a-zA-Z][\w-]*)\b([^>]*\bdata-component\s*=\s*"([^"]+)"[^>]*)>([\s\S]*?)<\/\1>/g;

  console.log("🔍 Looking for data-component blocks in HTML...");

  let matchCount = 0;
  const result = html.replace(
    compRe,
    (full, tag, attrs, compNameRaw, inner) => {
      matchCount++;
      const compName = norm(compNameRaw);
      const scope = getComponentScope(contentJSON, pageName, compName);

      console.log(`📦 Found component block #${matchCount}:`, {
        componentName: compName,
        hasScope: !!scope,
        scopeKeys: scope ? Object.keys(scope) : [],
        innerLength: inner.length,
      });

      if (!scope || typeof scope !== "object") {
        console.log(`⚠️ No scope found for component: ${compName}`);
        return full;
      }

      const processedInner = processComponentHTML(inner, scope);
      console.log(`✅ Processed component: ${compName}`);
      return `<${tag}${attrs}>${processedInner}</${tag}>`;
    }
  );

  console.log(`📊 Total component blocks found: ${matchCount}`);
  return result;
};

// Namespaced fallback: ${Component.key} OR ${Page.Component.key}
const injectNamespacedPlaceholders = (html, pageName, contentJSON) => {
  return html.replace(/\$\{([^}]+)\}/g, (m, raw) => {
    const parts = raw.split(".").map(norm);

    let val;
    if (parts.length === 2) {
      // ${Component.key}
      const [comp, key] = parts;
      val = deepGet(contentJSON, [pageName, comp, key]);
    } else if (parts.length === 3) {
      // ${Page.Component.key}
      const [page, comp, key] = parts;
      val = deepGet(contentJSON, [page, comp, key]);
    }

    if (val === undefined || val === null) return m; // keep original if not resolvable
    return String(val);
  });
};

// Main entry: inject into full HTML string
const injectContentIntoHTML = ({ html, contentJSON, pageName }) => {
  if (!html || !contentJSON || !pageName) return html;

  console.log("🔍 Content Injection Debug:", {
    pageName,
    contentJSON,
    htmlLength: html.length,
    hasDataComponent: html.includes("data-component"),
  });

  // Pass 1: component-scoped blocks
  let out = injectByComponentBlocks(html, pageName, contentJSON);
  console.log(
    "📝 After component blocks injection:",
    out.substring(0, 500) + "..."
  );

  // Pass 2: namespaced fallbacks
  out = injectNamespacedPlaceholders(out, pageName, contentJSON);
  console.log("✅ Final output:", out.substring(0, 500) + "...");

  return out;
};

/* ---------------------------
   Component
----------------------------*/

const SinglePagePreview = ({
  templatePage,
  previewMode,
  isDragging,
  contentJSON = {},
  isContentAdding = true,
}) => {
  const containerRef = useRef(null);
  const [scale, setScale] = useState(1);
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];

  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    const availableWidth = bounds.width - 15;
    const availableHeight = bounds.height - 15;
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    requestAnimationFrame(() => setScale(Math.min(widthScale, heightScale, 1)));
  };

  useEffect(() => {
    recalcScale();
    const ro = new ResizeObserver(recalcScale);
    if (containerRef.current) ro.observe(containerRef.current);
    return () => ro.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  const TemplatePagePreview = ({ templatePage }) => {
    const generatePagePreviewHTML = () => {
      if (!templatePage) return "";

      console.log("🔧 TemplatePagePreview - Generating HTML for:", {
        pageName: templatePage.name,
        isContentAdding,
        hasContentJSON: !!contentJSON && Object.keys(contentJSON).length > 0,
        fullPageContentLength: templatePage?.full_page_content?.length || 0,
      });

      // original base HTML for the page
      let html = generateGlobalPreviewHTML({
        type: "page",
        data: [],
        customCSS: templatePage.custom_css,
        customJS: templatePage.custom_js,
        title: templatePage.name || templatePage.name,
        fullData: templatePage?.full_page_content || "",
      });

      console.log("📄 Generated base HTML:", html.substring(0, 200) + "...");

      // inject dynamic content if enabled
      if (isContentAdding) {
        console.log("🚀 Starting content injection...");
        html = injectContentIntoHTML({
          html,
          contentJSON,
          pageName: templatePage.name, // e.g., "Final Testing Purpose"
        });
        console.log(
          "🎯 Final HTML after injection:",
          html.substring(0, 200) + "..."
        );
      } else {
        console.log("⚠️ Content injection disabled");
      }

      return html;
    };

    return (
      <div
        className="tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative"
        style={{
          height: "auto",
          minHeight: previewMode === "laptop" ? "320px" : "490px",
        }}
      >
        <div
          className="device-frame tw-bg-white tw-rounded-xl tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            transform: `scale(${scale})`,
            left: "50%",
            top: "50%",
            marginLeft: `-${deviceWidth / 2}px`,
            marginTop: `-${
              deviceHeight / (previewMode === "laptop" ? 2.4 : 2.4)
            }px`,
            transition: "all 0.3s ease",
            zIndex: isDragging ? 0 : 25,
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-rounded-xl">
            {templatePage ? (
              <iframe
                srcDoc={generatePagePreviewHTML()}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl tw-relative"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: isDragging ? "none" : "auto",
                  background: "#fff",
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        height: "auto",
        minHeight: previewMode === "laptop" ? `${29}rem` : `${42}rem`,
      }}
      className="tw-w-full tw-overflow-auto tw-relative tw-rounded-lg"
    >
      <TemplatePagePreview key={templatePage.id} templatePage={templatePage} />
    </div>
  );
};

// Export the injection function for testing
export { injectContentIntoHTML };

export default SinglePagePreview;
